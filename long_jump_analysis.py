import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
import seaborn as sns
from scipy.signal import find_peaks, savgol_filter
from scipy.stats import pearsonr
import warnings
warnings.filterwarnings('ignore') # 忽略警告

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 读取数据
def load_data():
    """加载运动者数据"""
    df1 = pd.read_excel('运动者1的跳远位置信息.xlsx')
    df2 = pd.read_excel('运动者2的跳远位置信息.xlsx')
    
    # 读取成绩
    with open('运动者1和运动者2的跳远成绩.txt', 'r', encoding='utf-8') as f:
        lines = f.readlines()
        score1 = float(lines[0].split()[1].replace('米', ''))
        score2 = float(lines[1].split()[1].replace('米', ''))
    
    return df1, df2, score1, score2

# 数据预处理
def preprocess_data(df, athlete_name="运动者"):
    """预处理数据，提取关键点坐标，处理异常值和缺失值"""
    print(f"\n开始预处理{athlete_name}数据...")
    print(f"原始数据形状: {df.shape}")

    frames = df['帧号'].values

    # 提取33个关键点的坐标
    keypoints = {}
    missing_count = 0
    outlier_count = 0

    for i in range(33):
        x_col = f'{i}_X'
        y_col = f'{i}_Y'
        if x_col in df.columns and y_col in df.columns:
            x_data = df[x_col].values
            y_data = df[y_col].values

            # 统计缺失值
            x_missing = np.isnan(x_data).sum()
            y_missing = np.isnan(y_data).sum()
            missing_count += x_missing + y_missing

            # 处理缺失值 - 使用线性插值
            if x_missing > 0 or y_missing > 0:
                print(f"  关键点{i}: X缺失{x_missing}个, Y缺失{y_missing}个")

                # 创建DataFrame进行插值
                temp_df = pd.DataFrame({'x': x_data, 'y': y_data})
                temp_df = temp_df.interpolate(method='linear', limit_direction='both')

                # 如果首尾仍有缺失值，用前向/后向填充
                temp_df = temp_df.fillna(method='bfill').fillna(method='ffill')

                x_data = temp_df['x'].values
                y_data = temp_df['y'].values

            # 检测和处理异常值 - 使用IQR方法
            def detect_outliers(data):
                Q1 = np.percentile(data, 25)
                Q3 = np.percentile(data, 75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                return (data < lower_bound) | (data > upper_bound)

            x_outliers = detect_outliers(x_data)
            y_outliers = detect_outliers(y_data)

            if x_outliers.sum() > 0 or y_outliers.sum() > 0:
                print(f"  关键点{i}: X异常值{x_outliers.sum()}个, Y异常值{y_outliers.sum()}个")
                outlier_count += x_outliers.sum() + y_outliers.sum()

                # 用中位数替换异常值
                x_data[x_outliers] = np.median(x_data[~x_outliers])
                y_data[y_outliers] = np.median(y_data[~y_outliers])

            # 平滑处理 - 使用移动平均
            if len(x_data) > 3:
                x_data = pd.Series(x_data).rolling(window=3, center=True, min_periods=1).mean().values
                y_data = pd.Series(y_data).rolling(window=3, center=True, min_periods=1).mean().values

            keypoints[i] = {
                'x': x_data,
                'y': y_data
            }

    # 数据质量检查
    print(f"\n{athlete_name}数据预处理完成:")
    print(f"  总帧数: {len(frames)}")
    print(f"  关键点数量: {len(keypoints)}")
    print(f"  处理的缺失值: {missing_count}个")
    print(f"  处理的异常值: {outlier_count}个")

    # 检查数据完整性
    complete_frames = 0
    for frame_idx in range(len(frames)):
        frame_complete = True
        for point_id in keypoints:
            x = keypoints[point_id]['x'][frame_idx]
            y = keypoints[point_id]['y'][frame_idx]
            if np.isnan(x) or np.isnan(y):
                frame_complete = False
                break
        if frame_complete:
            complete_frames += 1

    print(f"  完整帧数: {complete_frames}/{len(frames)} ({complete_frames/len(frames)*100:.1f}%)")

    # 计算数据范围
    all_x = []
    all_y = []
    for point_id in keypoints:
        all_x.extend(keypoints[point_id]['x'])
        all_y.extend(keypoints[point_id]['y'])

    print(f"  X坐标范围: [{np.min(all_x):.1f}, {np.max(all_x):.1f}]")
    print(f"  Y坐标范围: [{np.min(all_y):.1f}, {np.max(all_y):.1f}]")

    return frames, keypoints

# 计算重心位置
def calculate_center_of_mass(keypoints, frames):
    """计算人体重心位置"""
    # 主要关键点权重（基于人体解剖学）
    weights = {
        0: 0.08,   # 鼻子
        1: 0.08, 2: 0.08,  # 眼睛
        3: 0.08, 4: 0.08,  # 耳朵
        5: 0.15, 6: 0.15,  # 肩膀
        7: 0.08, 8: 0.08,  # 肘部
        9: 0.05, 10: 0.05, # 手腕
        11: 0.20, 12: 0.20, # 髋部
        13: 0.15, 14: 0.15, # 膝盖
        15: 0.08, 16: 0.08, # 脚踝
    }
    
    com_x = np.zeros(len(frames))
    com_y = np.zeros(len(frames))
    
    for frame_idx in range(len(frames)):
        total_weight = 0
        weighted_x = 0
        weighted_y = 0
        
        for point_id, weight in weights.items():
            if point_id in keypoints:
                x = keypoints[point_id]['x'][frame_idx]
                y = keypoints[point_id]['y'][frame_idx]
                
                # 检查是否为有效坐标
                if not (np.isnan(x) or np.isnan(y)):
                    weighted_x += x * weight
                    weighted_y += y * weight
                    total_weight += weight
        
        if total_weight > 0:
            com_x[frame_idx] = weighted_x / total_weight
            com_y[frame_idx] = weighted_y / total_weight
        else:
            # 如果没有有效点，使用前一帧的值
            if frame_idx > 0:
                com_x[frame_idx] = com_x[frame_idx-1]
                com_y[frame_idx] = com_y[frame_idx-1]
    
    return com_x, com_y

# 检测起跳和落地时刻
def detect_takeoff_landing(com_x, com_y, frames, fps=30):
    """检测起跳和落地时刻"""
    # 平滑处理
    if len(com_y) > 5:
        com_y_smooth = savgol_filter(com_y, window_length=5, polyorder=2)
        com_x_smooth = savgol_filter(com_x, window_length=5, polyorder=2)
    else:
        com_y_smooth = com_y
        com_x_smooth = com_x
    
    # 计算垂直速度和加速度
    dt = 1.0 / fps
    velocity_y = np.gradient(com_y_smooth, dt)
    acceleration_y = np.gradient(velocity_y, dt)
    
    # 计算水平位移
    horizontal_displacement = com_x_smooth - com_x_smooth[0]
    
    # 检测起跳时刻：垂直速度从负变正的时刻
    takeoff_candidates = []
    for i in range(1, len(velocity_y)-1):
        if velocity_y[i-1] <= 0 and velocity_y[i] > 0:
            takeoff_candidates.append(i)
    
    # 选择最显著的起跳时刻
    if takeoff_candidates:
        takeoff_frame = takeoff_candidates[0]
    else:
        # 备选方案：找到垂直位置开始显著上升的点
        takeoff_frame = np.argmax(np.diff(com_y_smooth[:len(com_y_smooth)//2])) + 1
    
    # 检测落地时刻：在起跳后，垂直速度从正变负且垂直位置开始下降
    landing_candidates = []
    for i in range(takeoff_frame + 10, len(velocity_y)-1):
        if velocity_y[i] > 0 and velocity_y[i+1] <= 0:
            landing_candidates.append(i)
    
    if landing_candidates:
        landing_frame = landing_candidates[0]
    else:
        # 备选方案：找到起跳后垂直位置的最高点后开始下降的点
        max_height_idx = np.argmax(com_y_smooth[takeoff_frame:]) + takeoff_frame
        landing_frame = min(max_height_idx + 20, len(frames) - 1)
    
    return takeoff_frame, landing_frame

# 分析滞空阶段运动
def analyze_flight_phase(com_x, com_y, keypoints, takeoff_frame, landing_frame, frames):
    """分析滞空阶段的运动过程"""
    flight_frames = frames[takeoff_frame:landing_frame+1]
    flight_com_x = com_x[takeoff_frame:landing_frame+1]
    flight_com_y = com_y[takeoff_frame:landing_frame+1]
    
    # 计算飞行时间
    flight_time = len(flight_frames) / 30.0  # 假设30fps
    
    # 计算水平和垂直位移
    horizontal_distance = flight_com_x[-1] - flight_com_x[0]
    max_height = np.max(flight_com_y) - flight_com_y[0]
    
    # 计算初始速度
    dt = 1.0 / 30.0
    initial_velocity_x = (flight_com_x[1] - flight_com_x[0]) / dt
    initial_velocity_y = (flight_com_y[1] - flight_com_y[0]) / dt
    
    # 分析关键身体部位在滞空阶段的运动
    body_parts_analysis = {}
    key_parts = {
        '头部': [0, 1, 2, 3, 4],  # 鼻子、眼睛、耳朵
        '躯干': [5, 6, 11, 12],   # 肩膀、髋部
        '手臂': [7, 8, 9, 10],    # 肘部、手腕
        '腿部': [13, 14, 15, 16]  # 膝盖、脚踝
    }
    
    for part_name, point_ids in key_parts.items():
        part_x = []
        part_y = []
        for point_id in point_ids:
            if point_id in keypoints:
                part_x.extend(keypoints[point_id]['x'][takeoff_frame:landing_frame+1])
                part_y.extend(keypoints[point_id]['y'][takeoff_frame:landing_frame+1])
        
        if part_x and part_y:
            body_parts_analysis[part_name] = {
                'x_range': np.max(part_x) - np.min(part_x),
                'y_range': np.max(part_y) - np.min(part_y),
                'x_trajectory': part_x,
                'y_trajectory': part_y
            }
    
    return {
        'flight_time': flight_time,
        'horizontal_distance': horizontal_distance,
        'max_height': max_height,
        'initial_velocity_x': initial_velocity_x,
        'initial_velocity_y': initial_velocity_y,
        'flight_frames': flight_frames,
        'flight_com_x': flight_com_x,
        'flight_com_y': flight_com_y,
        'body_parts_analysis': body_parts_analysis
    }

# 创建3D可视化
def create_3d_visualization(athlete_data, athlete_name, score):
    """创建3D可视化"""
    from mpl_toolkits.mplot3d import Axes3D
    
    frames, keypoints = athlete_data['frames'], athlete_data['keypoints']
    com_x, com_y = athlete_data['com_x'], athlete_data['com_y']
    takeoff_frame, landing_frame = athlete_data['takeoff_frame'], athlete_data['landing_frame']
    
    fig = plt.figure(figsize=(15, 10))
    
    # 3D轨迹图
    ax = fig.add_subplot(221, projection='3d')
    time = frames / 30.0
    ax.plot(com_x, time, com_y, 'b-', linewidth=2, alpha=0.8)
    ax.scatter(com_x[takeoff_frame], time[takeoff_frame], com_y[takeoff_frame], 
               color='green', s=100, label='起跳')
    ax.scatter(com_x[landing_frame], time[landing_frame], com_y[landing_frame], 
               color='red', s=100, label='落地')
    ax.set_xlabel('水平位置 (像素)')
    ax.set_ylabel('时间 (秒)')
    ax.set_zlabel('垂直位置 (像素)')
    ax.set_title(f'{athlete_name} - 3D运动轨迹')
    ax.legend()
    
    plt.tight_layout()
    plt.savefig(f'{athlete_name}_3d_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

# 可视化函数
def create_visualizations(athlete_data, athlete_name, score):
    """创建高级可视化图表"""

    frames, keypoints = athlete_data['frames'], athlete_data['keypoints']
    com_x, com_y = athlete_data['com_x'], athlete_data['com_y']
    takeoff_frame, landing_frame = athlete_data['takeoff_frame'], athlete_data['landing_frame']
    flight_analysis = athlete_data['flight_analysis']

    # 创建图形
    fig = plt.figure(figsize=(20, 15))

    # 1. 整体运动轨迹
    ax1 = plt.subplot(3, 3, 1)
    plt.plot(com_x, com_y, 'b-', linewidth=2, alpha=0.7, label='重心轨迹')
    plt.scatter(com_x[takeoff_frame], com_y[takeoff_frame], color='green', s=100, label='起跳点', zorder=5)
    plt.scatter(com_x[landing_frame], com_y[landing_frame], color='red', s=100, label='落地点', zorder=5)
    plt.xlabel('水平位置 (像素)')
    plt.ylabel('垂直位置 (像素)')
    plt.title(f'{athlete_name} - 重心运动轨迹\n成绩: {score}米')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 2. 垂直位置随时间变化
    ax2 = plt.subplot(3, 3, 2)
    time = frames / 30.0  # 转换为秒
    plt.plot(time, com_y, 'b-', linewidth=2)
    plt.axvline(x=time[takeoff_frame], color='green', linestyle='--', label='起跳时刻')
    plt.axvline(x=time[landing_frame], color='red', linestyle='--', label='落地时刻')
    plt.fill_between(time[takeoff_frame:landing_frame+1],
                     com_y[takeoff_frame:landing_frame+1],
                     alpha=0.3, color='yellow', label='滞空阶段')
    plt.xlabel('时间 (秒)')
    plt.ylabel('垂直位置 (像素)')
    plt.title('垂直位置-时间曲线')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 3. 水平位置随时间变化
    ax3 = plt.subplot(3, 3, 3)
    plt.plot(time, com_x, 'r-', linewidth=2)
    plt.axvline(x=time[takeoff_frame], color='green', linestyle='--', label='起跳时刻')
    plt.axvline(x=time[landing_frame], color='red', linestyle='--', label='落地时刻')
    plt.xlabel('时间 (秒)')
    plt.ylabel('水平位置 (像素)')
    plt.title('水平位移-时间曲线')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 4. 速度分析
    ax4 = plt.subplot(3, 3, 4)
    dt = 1.0 / 30.0
    velocity_x = np.gradient(com_x, dt)
    velocity_y = np.gradient(com_y, dt)
    velocity_magnitude = np.sqrt(velocity_x**2 + velocity_y**2)

    plt.plot(time, velocity_x, label='水平速度', linewidth=2)
    plt.plot(time, velocity_y, label='垂直速度', linewidth=2)
    plt.plot(time, velocity_magnitude, label='速度大小', linewidth=2, linestyle='--')
    plt.axvline(x=time[takeoff_frame], color='green', linestyle='--', alpha=0.7)
    plt.axvline(x=time[landing_frame], color='red', linestyle='--', alpha=0.7)
    plt.xlabel('时间 (秒)')
    plt.ylabel('速度 (像素/秒)')
    plt.title('速度分析')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 5. 滞空阶段轨迹
    ax5 = plt.subplot(3, 3, 5)
    flight_x = flight_analysis['flight_com_x']
    flight_y = flight_analysis['flight_com_y']
    plt.plot(flight_x, flight_y, 'purple', linewidth=3, marker='o', markersize=4)
    plt.scatter(flight_x[0], flight_y[0], color='green', s=150, label='起跳', zorder=5)
    plt.scatter(flight_x[-1], flight_y[-1], color='red', s=150, label='落地', zorder=5)
    plt.xlabel('水平位置 (像素)')
    plt.ylabel('垂直位置 (像素)')
    plt.title(f'滞空阶段轨迹\n飞行时间: {flight_analysis["flight_time"]:.3f}秒')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 6. 身体部位运动范围
    ax6 = plt.subplot(3, 3, 6)
    body_parts = flight_analysis['body_parts_analysis']
    if body_parts:
        parts = list(body_parts.keys())
        x_ranges = [body_parts[part]['x_range'] for part in parts]
        y_ranges = [body_parts[part]['y_range'] for part in parts]

        x = np.arange(len(parts))
        width = 0.35

        plt.bar(x - width/2, x_ranges, width, label='水平运动范围', alpha=0.8)
        plt.bar(x + width/2, y_ranges, width, label='垂直运动范围', alpha=0.8)
        plt.xlabel('身体部位')
        plt.ylabel('运动范围 (像素)')
        plt.title('滞空阶段身体部位运动范围')
        plt.xticks(x, parts, rotation=45)
        plt.legend()
        plt.grid(True, alpha=0.3)

    # 7. 关键参数总结
    ax7 = plt.subplot(3, 3, 7)
    ax7.axis('off')

    params_text = f"""
    运动参数总结:

    • 跳远成绩: {score} 米
    • 起跳帧: {takeoff_frame}
    • 落地帧: {landing_frame}
    • 滞空时间: {flight_analysis['flight_time']:.3f} 秒
    • 水平位移: {flight_analysis['horizontal_distance']:.1f} 像素
    • 最大高度: {flight_analysis['max_height']:.1f} 像素
    • 初始水平速度: {flight_analysis['initial_velocity_x']:.1f} 像素/秒
    • 初始垂直速度: {flight_analysis['initial_velocity_y']:.1f} 像素/秒
    """

    plt.text(0.1, 0.9, params_text, transform=ax7.transAxes, fontsize=12,
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

    # 8. 关键点轨迹热力图
    ax8 = plt.subplot(3, 3, 8)

    # 创建关键点密度图
    all_x = []
    all_y = []
    for point_id in keypoints:
        all_x.extend(keypoints[point_id]['x'][takeoff_frame:landing_frame+1])
        all_y.extend(keypoints[point_id]['y'][takeoff_frame:landing_frame+1])

    if all_x and all_y:
        plt.hist2d(all_x, all_y, bins=20, cmap='YlOrRd', alpha=0.8)
        plt.colorbar(label='密度')
        plt.xlabel('水平位置 (像素)')
        plt.ylabel('垂直位置 (像素)')
        plt.title('滞空阶段关键点分布热力图')

    # 9. 运动轨迹的抛物线拟合
    ax9 = plt.subplot(3, 3, 9)

    # 对滞空阶段进行抛物线拟合
    flight_x_norm = flight_x - flight_x[0]  # 归一化起始位置
    flight_y_norm = flight_y - flight_y[0]

    if len(flight_x_norm) > 3:
        # 二次多项式拟合
        coeffs = np.polyfit(flight_x_norm, flight_y_norm, 2)
        x_fit = np.linspace(0, flight_x_norm[-1], 100)
        y_fit = np.polyval(coeffs, x_fit)

        plt.plot(flight_x_norm, flight_y_norm, 'bo-', label='实际轨迹', markersize=6)
        plt.plot(x_fit, y_fit, 'r--', label='抛物线拟合', linewidth=2)
        plt.xlabel('相对水平位移 (像素)')
        plt.ylabel('相对垂直位移 (像素)')
        plt.title('滞空轨迹抛物线拟合')
        plt.legend()
        plt.grid(True, alpha=0.3)

        # 计算拟合优度
        y_pred = np.polyval(coeffs, flight_x_norm)
        r2 = 1 - np.sum((flight_y_norm - y_pred)**2) / np.sum((flight_y_norm - np.mean(flight_y_norm))**2)
        plt.text(0.05, 0.95, f'R² = {r2:.3f}', transform=ax9.transAxes,
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

    plt.tight_layout()
    plt.savefig(f'{athlete_name}_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

# 主函数
def main():
    """主分析函数"""
    print("正在加载数据...")
    df1, df2, score1, score2 = load_data()

    # 分析运动者1
    print("分析运动者1...")
    frames1, keypoints1 = preprocess_data(df1)
    com_x1, com_y1 = calculate_center_of_mass(keypoints1, frames1)
    takeoff1, landing1 = detect_takeoff_landing(com_x1, com_y1, frames1)
    flight_analysis1 = analyze_flight_phase(com_x1, com_y1, keypoints1, takeoff1, landing1, frames1)

    athlete1_data = {
        'frames': frames1,
        'keypoints': keypoints1,
        'com_x': com_x1,
        'com_y': com_y1,
        'takeoff_frame': takeoff1,
        'landing_frame': landing1,
        'flight_analysis': flight_analysis1
    }

    # 分析运动者2
    print("分析运动者2...")
    frames2, keypoints2 = preprocess_data(df2)
    com_x2, com_y2 = calculate_center_of_mass(keypoints2, frames2)
    takeoff2, landing2 = detect_takeoff_landing(com_x2, com_y2, frames2)
    flight_analysis2 = analyze_flight_phase(com_x2, com_y2, keypoints2, takeoff2, landing2, frames2)

    athlete2_data = {
        'frames': frames2,
        'keypoints': keypoints2,
        'com_x': com_x2,
        'com_y': com_y2,
        'takeoff_frame': takeoff2,
        'landing_frame': landing2,
        'flight_analysis': flight_analysis2
    }

    # 创建可视化
    print("创建可视化图表...")
    create_visualizations(athlete1_data, "运动者1", score1)
    create_visualizations(athlete2_data, "运动者2", score2)
    create_3d_visualization(athlete1_data, "运动者1", score1)
    create_3d_visualization(athlete2_data, "运动者2", score2)

    # 输出分析结果
    print("\n" + "="*60)
    print("立定跳远运动分析结果")
    print("="*60)

    print(f"\n运动者1 (成绩: {score1}米):")
    print(f"  起跳时刻: 第{takeoff1}帧 ({takeoff1/30:.2f}秒)")
    print(f"  落地时刻: 第{landing1}帧 ({landing1/30:.2f}秒)")
    print(f"  滞空时间: {flight_analysis1['flight_time']:.3f}秒")
    print(f"  水平位移: {flight_analysis1['horizontal_distance']:.1f}像素")
    print(f"  最大高度: {flight_analysis1['max_height']:.1f}像素")
    print(f"  初始水平速度: {flight_analysis1['initial_velocity_x']:.1f}像素/秒")
    print(f"  初始垂直速度: {flight_analysis1['initial_velocity_y']:.1f}像素/秒")

    print(f"\n运动者2 (成绩: {score2}米):")
    print(f"  起跳时刻: 第{takeoff2}帧 ({takeoff2/30:.2f}秒)")
    print(f"  落地时刻: 第{landing2}帧 ({landing2/30:.2f}秒)")
    print(f"  滞空时间: {flight_analysis2['flight_time']:.3f}秒")
    print(f"  水平位移: {flight_analysis2['horizontal_distance']:.1f}像素")
    print(f"  最大高度: {flight_analysis2['max_height']:.1f}像素")
    print(f"  初始水平速度: {flight_analysis2['initial_velocity_x']:.1f}像素/秒")
    print(f"  初始垂直速度: {flight_analysis2['initial_velocity_y']:.1f}像素/秒")

    # 比较分析
    print(f"\n比较分析:")
    print(f"  成绩差异: {abs(score1-score2):.2f}米")
    print(f"  滞空时间差异: {abs(flight_analysis1['flight_time']-flight_analysis2['flight_time']):.3f}秒")
    print(f"  水平位移差异: {abs(flight_analysis1['horizontal_distance']-flight_analysis2['horizontal_distance']):.1f}像素")

    # 创建对比图表
    create_comparison_chart(athlete1_data, athlete2_data, score1, score2)

    return athlete1_data, athlete2_data

# 创建对比图表
def create_comparison_chart(athlete1_data, athlete2_data, score1, score2):
    """创建两位运动者的对比图表"""
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))

    # 重心轨迹对比
    ax1 = axes[0, 0]
    ax1.plot(athlete1_data['com_x'], athlete1_data['com_y'], 'b-', linewidth=2, label=f'运动者1 ({score1}m)', alpha=0.8)
    ax1.plot(athlete2_data['com_x'], athlete2_data['com_y'], 'r-', linewidth=2, label=f'运动者2 ({score2}m)', alpha=0.8)
    ax1.scatter(athlete1_data['com_x'][athlete1_data['takeoff_frame']],
               athlete1_data['com_y'][athlete1_data['takeoff_frame']], color='blue', s=100, marker='^')
    ax1.scatter(athlete2_data['com_x'][athlete2_data['takeoff_frame']],
               athlete2_data['com_y'][athlete2_data['takeoff_frame']], color='red', s=100, marker='^')
    ax1.set_xlabel('水平位置 (像素)')
    ax1.set_ylabel('垂直位置 (像素)')
    ax1.set_title('重心轨迹对比')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 滞空轨迹对比
    ax2 = axes[0, 1]
    flight1 = athlete1_data['flight_analysis']
    flight2 = athlete2_data['flight_analysis']
    ax2.plot(flight1['flight_com_x'], flight1['flight_com_y'], 'b-', linewidth=3, label=f'运动者1', alpha=0.8)
    ax2.plot(flight2['flight_com_x'], flight2['flight_com_y'], 'r-', linewidth=3, label=f'运动者2', alpha=0.8)
    ax2.set_xlabel('水平位置 (像素)')
    ax2.set_ylabel('垂直位置 (像素)')
    ax2.set_title('滞空阶段轨迹对比')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    # 参数对比柱状图
    ax3 = axes[0, 2]
    params = ['滞空时间(s)', '水平位移(px)', '最大高度(px)']
    values1 = [flight1['flight_time'], flight1['horizontal_distance'], flight1['max_height']]
    values2 = [flight2['flight_time'], flight2['horizontal_distance'], flight2['max_height']]

    x = np.arange(len(params))
    width = 0.35

    ax3.bar(x - width/2, values1, width, label='运动者1', alpha=0.8)
    ax3.bar(x + width/2, values2, width, label='运动者2', alpha=0.8)
    ax3.set_ylabel('数值')
    ax3.set_title('关键参数对比')
    ax3.set_xticks(x)
    ax3.set_xticklabels(params, rotation=45)
    ax3.legend()
    ax3.grid(True, alpha=0.3)

    # 速度对比
    ax4 = axes[1, 0]
    time1 = athlete1_data['frames'] / 30.0
    time2 = athlete2_data['frames'] / 30.0
    velocity1_x = np.gradient(athlete1_data['com_x'], 1/30.0)
    velocity2_x = np.gradient(athlete2_data['com_x'], 1/30.0)

    ax4.plot(time1, velocity1_x, 'b-', linewidth=2, label='运动者1', alpha=0.8)
    ax4.plot(time2, velocity2_x, 'r-', linewidth=2, label='运动者2', alpha=0.8)
    ax4.axvline(x=time1[athlete1_data['takeoff_frame']], color='blue', linestyle='--', alpha=0.7)
    ax4.axvline(x=time2[athlete2_data['takeoff_frame']], color='red', linestyle='--', alpha=0.7)
    ax4.set_xlabel('时间 (秒)')
    ax4.set_ylabel('水平速度 (像素/秒)')
    ax4.set_title('水平速度对比')
    ax4.legend()
    ax4.grid(True, alpha=0.3)

    # 垂直位置对比
    ax5 = axes[1, 1]
    ax5.plot(time1, athlete1_data['com_y'], 'b-', linewidth=2, label='运动者1', alpha=0.8)
    ax5.plot(time2, athlete2_data['com_y'], 'r-', linewidth=2, label='运动者2', alpha=0.8)
    ax5.axvline(x=time1[athlete1_data['takeoff_frame']], color='blue', linestyle='--', alpha=0.7)
    ax5.axvline(x=time2[athlete2_data['takeoff_frame']], color='red', linestyle='--', alpha=0.7)
    ax5.set_xlabel('时间 (秒)')
    ax5.set_ylabel('垂直位置 (像素)')
    ax5.set_title('垂直位置对比')
    ax5.legend()
    ax5.grid(True, alpha=0.3)

    # 成绩对比
    ax6 = axes[1, 2]
    athletes = ['运动者1', '运动者2']
    scores = [score1, score2]
    colors = ['blue', 'red']

    bars = ax6.bar(athletes, scores, color=colors, alpha=0.7)
    ax6.set_ylabel('跳远成绩 (米)')
    ax6.set_title('跳远成绩对比')
    ax6.grid(True, alpha=0.3)

    # 在柱状图上添加数值标签
    for bar, score in zip(bars, scores):
        height = bar.get_height()
        ax6.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{score}m', ha='center', va='bottom', fontweight='bold')

    plt.tight_layout()
    plt.savefig('运动者对比分析.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    print("立定跳远运动分析程序启动...")
    athlete1_data, athlete2_data = main()
